'use client';

import React from 'react';

interface CardData {
  title: string;
  content: string | React.ReactNode;
}

export default function ReflectionPage() {
  const reflectionCardData: CardData[] = [
    {
      title: "Reflection on Research Process",
      content: (
        <>
          <p className="mb-3">The core research question, "How do I design, build, and use system(s) that discover, assess, and integrate emerging AI research, methodologies, and approaches into my audio software development workflow?" was as much a result of my process of developing this tool as the tool was an attempt to answer the research question; they both evolved together over time to become what they are currently.</p>
          <p className="mb-3">I am satisfied with the evolution of my research question. Looking back at them, I can see that I progressively narrowed down the scope of the question until I arrived at the one currently being asked. At the time, it was not clear to me that I was being too broad with the question. This is definitely a lesson I've learned, and if I am ever in the same situation, I will definitely apply this again.</p>
        </>
      )
    },
    {
      title: "Tool Development and Usage",
      content: (
        <>
          <p className="mb-3">The largest factors in deciding the type of output we get are: 1. The LLM used for querying. 2. The 'System Prompt' (this typically refers to the prompt behind the scenes that is appended to our prompts when using a frontier LLM. In our system, it also functions similarly to, but is not a true system prompt, just the extra context we decide to provide to the model).</p>
          <p className="mb-3">A potential way to address this inherent subjectivity and bias in the outputs in the evaluations could be to use a system similar to LMArena (https://beta.lmarena.ai/about). We could instruct LLMs to generate evaluation templates and research goals and objectives based on a topic or area of research, and then have humans compare the output of these different templates and contextual documents and choose the ones they like the most. In a sort of tournament-brackets style contest, it would eventually show us which templates and descriptions yield output that humans prefer the most, which could then be used as the main system prompt. This is, of course, way out of the scope of this project, but I find it worth mentioning because it would be an interesting approach to finding better templates and prompts.</p>
          <h5 className="font-semibold mt-4 mb-2 text-gray-800 dark:text-gray-200">Impact on My Research & Development Section</h5>
          <p className="mb-3">The 'Impact on My Research & Development' section within the generated evaluations serves as a crucial bridge between abstract academic findings and my concrete audio software development workflow. For instance, if a paper details a novel audio synthesis technique, this section prompts the LLM to consider its applicability for creating new virtual instruments or sound effects within my projects. Similarly, if research highlights an innovative approach to audio feature extraction, the evaluation would explore how this could enhance my existing music information retrieval tools or sound analysis scripts. It encourages a practical lens, asking: 'How can this research directly influence or enhance the tools, plugins, and methods I use daily?' This targeted reflection helps unearth actionable insights that might otherwise be missed when reading broadly, such as identifying a new DSP algorithm suitable for a plugin I'm developing, or a machine learning architecture that could improve a sound classification model.</p>
        </>
      )
    },
    {
      title: "Key Learnings: Technical Skills",
      content: (
        <>
          <p className="mb-3">Designing, building, and using this system has definitely opened my eyes to how we can personally create these tools to help us instead of relying on third parties, enabled by increased capability in AI-assisted development tools together with a personal improvement in coding proficiency. Not to mention the information obtained while conceptualizing, researching, and testing this system, which also widened my knowledge about what was possible with current technology.</p>
        </>
      )
    },
    {
      title: "Key Learnings: Research and Critical Thinking",
      content: (
        <>
          <p className="mb-3">We should also question the validity of any output produced by these models. How realistic and feasible is it truly to translate abstract and domain-specific knowledge to a different field or practical work? I personally believe that while these LLMs are becoming masters-of-all more and more, they are not quite at the level of a PhD or a human equivalent that the scientific community would trust to manually do such a translation of information. We also cannot ignore the sycophantic tendencies of these models. Ask and you shall receive. While experimentation has proven that the prompt can greatly influence this behavior, when using external models, we cannot directly influence the system prompts of these models and therefore should always expect some sort of sycophancy.</p>
          <p className="mb-3">We have to keep in mind that we are responsible for these outputs and that we cannot blame the variance and imperfections of these models. We are the ones at the end of the chain that take the information and apply it elsewhere, and it should be our responsibility to ensure we do not take the information for granted and double-check critical pieces of information.</p>
        </>
      )
    },
    {
      title: "Addressing Challenges and Overcoming Obstacles",
      content: (
        <>
          <p className="mb-3">The research tool itself is not perfect, and I do not claim it to be. It serves simply as a tool to filter the vast amount of papers available so that we can read about papers in a different light, allowing us to make better judgment based on the available data. This data, in turn, can of course be hallucinated or biased, as LLMs can and are. The big distinction here is: imagine we manually fill in such an evaluation page. We could succeed in ensuring there are few to no hallucinations/biases present in the output, but probably by using up 10x or more time than if we outsourced this to the LLM. The gains in time are much larger than the losses in factuality that could be introduced by LLM-induced errors, and would be negated by still having a critical attitude towards its outputs and manually fact-checking critical information.</p>
          <p className="mb-3">If scaled up, the cost of processing many papers would be large. The system could be set up with enterprise-level API access and do batch processing to save money, or could require the user to enter their personal API key to pay for their own processing.</p>
          <p className="mb-3">Just like when manually reviewing a collection of papers in a short duration, we can still run into information overload when reading through all these evaluations. While the current system does summarize the paper, it is not instructed to produce information in such a way as to prevent overload. It is still a risk that persists even with this system.</p>
        </>
      )
    },
    {
      title: "Personal Growth and Future Outlook",
      content: (
        <>
          <p className="mb-3">By creating this research tool to answer my previously proposed research question, I discovered what I was really trying to answer: I'm not looking for a specific piece of knowledge or a technique. I want to create the right environment for myself to be able to continuously learn and discover new information. Now, this tool serves that purpose exactly, while I also learned a lot while building it in the first place. I'd say it has shifted my mentality from the want to always use the most effective and efficient tooling in my work, to fostering the right workflow that keeps you eager to learn and improve yourself, which I now think will be more effective in the long run.</p>
          <p className="mb-3">I now see that such a tool can be applied to any field of study. By changing the 'system prompt' and adapting it to other sectors or people, you can steer the output to be whatever you would like, which opens the possibility of fleshing out this system with a professional UI and creating a SaaS product out of it.</p>
          <p className="mb-3">If and when the functionality is implemented to automatically scan and evaluate papers from pre-print and peer-reviewed publication servers, it will be even more useful. This is the key priority going forward with this project after graduation.</p>
        </>
      )
    }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 font-merriweather">
      {/* Banner Section */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-white dark:text-white mb-4">
              Reflection
            </h1>
            <p className="text-xl text-white dark:text-white max-w-3xl mx-auto">
              Reflecting on the research process, outcomes, and personal learnings.
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Main Content Wrapper with Cards */}
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        <div className="space-y-8">
          {reflectionCardData.map((card, index) => (
            <div key={index} className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg overflow-hidden flex flex-col">
              <div className="h-1.5 bg-gradient-to-r from-blue-500 to-purple-600"></div>
              <div className="p-6 md:p-8 flex-grow">
                <h4 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  {card.title}
                </h4>
                {typeof card.content === 'string' ? (
                  <p className="text-gray-700 dark:text-gray-300">
                    {card.content}
                  </p>
                ) : (
                  <div className="text-gray-700 dark:text-gray-300 prose prose-sm sm:prose dark:prose-invert max-w-none">
                    {card.content}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        
      </div>
    </div>
  );
}
