'use client';

import { useState, ReactNode, ElementType } from 'react';
import { UserIcon, BookOpenIcon, LightBulbIcon, ChevronDownIcon, MagnifyingGlassIcon, CogIcon, ArrowPathIcon, RocketLaunchIcon, AcademicCapIcon, FlagIcon, KeyIcon, BoltIcon } from '@heroicons/react/24/outline';

interface CardDataItem {
  id: number;
  title: string;
  icon: ElementType;
  iconColorClass: string;
  content: ReactNode;
}

export default function Introduction() {
  const [openCardIndex, setOpenCardIndex] = useState<number | null>(null);

  const handleCardToggle = (index: number) => {
    setOpenCardIndex(prevIndex => (prevIndex === index ? null : index));
  };

  const cardData: CardDataItem[] = [
    {
      id: 0,
      title: "The Research Objective",
      icon: LightBulbIcon,
      iconColorClass: "border-t-teal-500 dark:border-t-teal-400",
      content: (
        <>
          {/* Section 1: Evolution of the Objective */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <LightBulbIcon className="h-6 w-6 mr-3 text-teal-500 dark:text-teal-400 shrink-0" />
              Finding the Right Question
            </h4>
            <div className="pl-9"> {/* Indent content under the icon */}
              <p className="text-gray-700 dark:text-gray-300 mb-2">
                The path to the current research objective involved an iterative process. Initially broad, the first topic was: "The Role of AI in expanding the creative process." This was later refined to "Effectively applying artificial intelligence as a music technologist."
              </p>
              <p className="text-gray-700 dark:text-gray-300">
                With this second iteration of the research question I began working on my supportive narrative. But even then, as described in my SN outline document (draft version of the concept for my supportive narrative that was handed in during the midterm of this year), I still considered this to be a working title. While working on this first version the definitive research question revealed itself to me.
              </p>
            </div>
          </div>

          {/* Section 2: The Core Research Question */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <MagnifyingGlassIcon className="h-6 w-6 mr-3 text-teal-500 dark:text-teal-400 shrink-0" />
              The Core Research Question
            </h4>
            <div className="pl-9 border-l-2 border-teal-500/30 dark:border-teal-400/30">
              <p className="py-2 px-3 font-medium text-lg text-teal-700 dark:text-teal-300">
                <em>How do I design, build and use system(s) that discover, assess, and integrate emerging AI research, methodologies and approaches into my audio software development workflow?</em>
              </p>
            </div>
          </div>

          {/* Section 3: The Realization */}
          <div>
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <KeyIcon className="h-6 w-6 mr-3 text-teal-500 dark:text-teal-400 shrink-0" />
              Key Realization
            </h4>
            <div className="pl-9">
              <p className="text-gray-700 dark:text-gray-300 mb-2">
                The more time I spent considering the possibility if certain techniques or approaches could improve my workflow, to conceptualize faster, design better software or debug more effectively. The more I realized that due to the ever-increasing speed of AI development and its expanding capabilities, these specific methods could quickly become irrelevant or even obsolete.
              </p>
              <p className="text-gray-700 dark:text-gray-300">
                This realization led to the current research question, which is centered around building systems and tools that can continuously adapt and integrate the latest AI advancements, rather than focusing on specific techniques that may quickly become outdated.
              </p>
            </div>
          </div>
        </>
      ),
    },
    {
      id: 1,
      title: "The Research Tool",
      icon: BookOpenIcon,
      iconColorClass: "border-t-purple-500 dark:border-t-purple-400",
      content: (
        <>
          {/* Section 1: How It Works */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <CogIcon className="h-6 w-6 mr-3 text-purple-500 dark:text-purple-400 shrink-0" />
              How The System Operates
            </h4>
            <p className="text-gray-700 dark:text-gray-300 mb-3 pl-9">
              The result is this website and the automated research tool, which are tightly coupled. The system operates as follows:
            </p>
            {/* List's border will align with h4 icon. Items indented pl-24 from border. */}
            <ol className="list-decimal list-outside space-y-2 pl-20 text-gray-700 dark:text-gray-300 border-l-2 border-purple-500/30 dark:border-purple-400/30 py-2">
              <li className="mb-1 pl-2">
                A research paper (in PDF format) is selected as input.
              </li>
              <li className="mb-1 pl-2">
                The PDF is converted to a markdown format using a neural network (I used the marker-pdf tool), making it interpretable for the LLM.
              </li>
              <li className="mb-1 pl-2">
                This markdown content, along with detailed instructions and an evaluation template, is fed to the LLM in a single prompt.
              </li>
              <li className="mb-1 pl-2">
                The LLM analyzes the paper's content and fills in the evaluation template according to the provided context and instructions.
              </li>
              <li className="pl-2">
                The completed evaluation, in JSON format, is then saved to the <code>public/papers/evaluations</code> directory.
              </li>
              <li className="pl-2">
                Now the contents from the filled in JSON evaluation template are used to dynamically populate the website.
              </li>
            </ol>
            <p className="text-gray-700 dark:text-gray-300 mb-3 pl-9">
             <br /><i>NOTE: The context, template and prompt used for this process can be viewed by navigating to the <code>method</code> page. </i>
            </p>
          </div>

          {/* Section 2: Dynamic Content & Usage */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <ArrowPathIcon className="h-6 w-6 mr-3 text-purple-500 dark:text-purple-400 shrink-0" />
              Dynamic Content & Practical Usage
            </h4>
            <div className="pl-7">
              <p className="text-gray-700 dark:text-gray-300 mb-3">
                The website is designed to dynamically load these JSON evaluation files. It then displays the contents of these evaluations in a user-friendly way. Due to this dynamic loading, simply adding a new JSON evaluation file to the evaluations directory automatically updates the website, adding the new paper to the list and displaying its contents and evaluation.
              </p>
              <p className="text-gray-700 dark:text-gray-300">
                After analyzing papers, I can manually scroll through the database and pick the most relevant based on the LLM assigned score and inspect its contents. I can go to the methods page where I can learn about how a paper's methods can be implemented in a step-by-step manner or in an audio software related task. Finally, there is the results page which describes contextualized insights, taking the paper's core ideas and applying them to relevant aspects of an audio software developers typical work.
              </p>
            </div>
          </div>

          {/* Section 3: Future Vision */}
          <div>
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <RocketLaunchIcon className="h-6 w-6 mr-3 text-purple-500 dark:text-purple-400 shrink-0" />
              Future Vision
            </h4>
            <p className="text-gray-700 dark:text-gray-300 mb-3 pl-9">
              While the tool is currently functional, I have more features that I wish to implement. The long-term vision for this project extends to a more autonomous AI-powered research assistant. Future iterations could include features like:
            </p>
            {/* List's border will align with h4 icon. Items indented pl-24 from border. */}
            <ul className="list-disc list-outside space-y-2 pl-20 text-gray-700 dark:text-gray-300 border-l-2 border-purple-500/30 dark:border-purple-400/30 py-2">
              <li className="mb-1 pl-2">Direct PDF uploads (with built in neural conversion to markdown) or uploading through a URL.</li>
              <li className="mb-1 pl-2">Automated scanning of pre-print servers (e.g., arXiv) and peer-reviewed journals and downloading relevant papers.</li>
              <li className="mb-1 pl-2">Automated evaluation of suitable papers, and added to the site if their evaluated score passes a threshold value.</li>
              <li className="mb-1 pl-2">Automated notifications (email, Slack) if a paper is autonomously evaluated, exceeds the threshold score and is added to the website.</li>
              <li className="pl-2">Allowing configuration of the prompt, evaluation criteria and context to allow for personalized use.</li>
            </ul>
          </div>
        </>
      ),
    },
    {
      id: 2,
      title: "The Author",
      icon: UserIcon,
      iconColorClass: "border-t-blue-500 dark:border-t-blue-400",
      content: (
        <>
          {/* Section 1: Initial Spark */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <BoltIcon className="h-6 w-6 mr-3 text-blue-500 dark:text-blue-400 shrink-0" />
              Acceleration
            </h4>
            <div className="pl-9">
              <p className="text-gray-700 dark:text-gray-300">
                After catching wind of the fast advancements in AI back in the summer of 2023, I was mind-blown and awe-inspired at the same time. The potential impact of this technology is enourmous and I was convinced that this technology was going to change the world, and that it would be wise to get familliar with it. <br /> <br />
                I feel fortunate to be at this position in my life during such a pivotal moment in human history. While many are skeptical, I am certain this will lead to scientific advancements like we have never seen and intend to follow its progress closely.
              </p>
            </div>
          </div>

          {/* Section 2: Bridging Interests & Studies */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <AcademicCapIcon className="h-6 w-6 mr-3 text-blue-500 dark:text-blue-400 shrink-0" />
              AI in Music Technology
            </h4>
            <div className="pl-9">
              <p className="text-gray-700 dark:text-gray-300">
                I was looking for a way to incorporate this new technology and newfound interestinto my studies and existing work and found the application of neural networks in audio plugins especially promising. With basically no prior experience but a strong drive to learn I managed to land an internship doing exactly this: learning how to build plugins and integrate neural networks into them. <br /> <br />
                This was a great success and further reinforced my feeling that I was on the right track. Ever since I have continued to work together with my former intership supervisor. And all our projects are audio and AI related, which is exactly what I wanted.
              </p>
            </div>
          </div>

          {/* Section 3: Goals and Aspirations */}
          <div>
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
              <FlagIcon className="h-6 w-6 mr-3 text-blue-500 dark:text-blue-400 shrink-0" />
              Goals
            </h4>
            <div className="pl-9">
              <p className="text-gray-700 dark:text-gray-300">
                I'm looking to improve my skills across the board and to stay in the now by applying AI tools into my workflow where compatible and useful, and building plugins that leverage neural networks for creative purposes. This supportive narrative serves as one of my attempts to realize these goals.
              </p>
            </div>
          </div>
        </>
      ),
    }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 font-merriweather">
      {/* Banner Section */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-white dark:text-white mb-4">
              Introduction
            </h1>
            <p className="text-xl text-white dark:text-white max-w-3xl mx-auto">
              Information about this supportive narrative, the tool and the author.
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Main Content Wrapper */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">


        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
          {cardData.map((card, index) => {
            let iconTextColorClass;
            if (card.title === "The Research Objective") {
              // Explicitly set for the problematic icon
              iconTextColorClass = "text-teal-500 dark:text-teal-400";
            } else {
              iconTextColorClass = card.iconColorClass.replaceAll('border-t-', 'text-');
            }

            return (
              <button
                key={card.id}
                onClick={() => handleCardToggle(index)}
                className={`w-full flex items-center justify-between p-4 md:p-6 text-left focus:outline-none rounded-lg shadow-sm dark:shadow-zinc-800/30 border border-gray-200 dark:border-zinc-700 border-t-4 ${card.iconColorClass} ${openCardIndex === index ? 'bg-gray-100 dark:bg-zinc-800 scale-105 shadow-md' : 'bg-white dark:bg-zinc-900 hover:bg-gray-50 dark:hover:bg-zinc-800/60'} transition-all duration-150 ease-in-out`}
              >
                <div className="flex items-center">
                  {card.icon && <card.icon className={`h-6 w-6 md:h-7 md:w-7 mr-3 ${iconTextColorClass}`} />}
                  <h2 className="text-lg md:text-xl font-semibold text-gray-800 dark:text-gray-100">{card.title}</h2>
                </div>
                <ChevronDownIcon
                  className={`h-5 w-5 md:h-6 md:w-6 text-gray-500 dark:text-gray-400 transform transition-transform duration-200 ${openCardIndex === index ? 'rotate-180' : ''}`}
                />
              </button>
            );
          })}
        </div>

        {openCardIndex !== null && (
          <div className={`bg-white dark:bg-zinc-900 rounded-lg shadow-lg dark:shadow-gray-800/30 border border-gray-200 dark:border-gray-700 p-6 md:p-8 border-t-4 ${cardData[openCardIndex].iconColorClass} mt-0 mb-12 animate-fadeIn`}>
            <style jsx global>{`
              @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
              }
              .animate-fadeIn {
                animation: fadeIn 0.3s ease-out forwards;
              }
            `}</style>
            <div className="prose prose-sm sm:prose-base dark:prose-invert max-w-none text-gray-700 dark:text-gray-300">
              {cardData[openCardIndex].content}
            </div>
          </div>
        )}

        <div className="mb-8 rounded-lg shadow-lg dark:shadow-gray-800/30 bg-gradient-to-r from-teal-500 via-purple-500 to-blue-500 pt-1">
          <div className="bg-white dark:bg-zinc-900 rounded-b-lg p-6 md:p-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">Research Focus Areas</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="flex flex-col items-center p-6 bg-white dark:bg-zinc-900 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 border-t-4 border-t-teal-500 dark:border-t-teal-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-teal-500 dark:text-teal-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M17.25 6.75L22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3l-4.5 16.5" />
                </svg>
                <h3 className="text-xl font-semibold text-center text-gray-900 dark:text-gray-100 mb-2">AI-Augmented Development</h3>
                <p className="text-base text-center text-gray-700 dark:text-gray-300">Exploring how AI can enhance writing, debugging, and testing code</p>
              </div>

              <div className="flex flex-col items-center p-6 bg-white dark:bg-zinc-900 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 border-t-4 border-t-purple-500 dark:border-t-purple-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-purple-500 dark:text-purple-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5} >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.5 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.898 20.624l.211.462A.75.75 0 0017.75 22a.75.75 0 00.64-.874l-.211-.462m0 0l.462-.211a.75.75 0 00-.874-.64L16.898 20.624z" />
                </svg>
                <h3 className="text-xl font-semibold text-center text-gray-900 dark:text-gray-100 mb-2">Prompt Engineering</h3>
                <p className="text-base text-center text-gray-700 dark:text-gray-300">Exploring prompting techniques and their potential impact on AI-generated code</p>
              </div>

              <div className="flex flex-col items-center p-6 bg-white dark:bg-zinc-900 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 border-t-4 border-t-blue-500 dark:border-t-blue-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-blue-500 dark:text-blue-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                </svg>
                <h3 className="text-xl font-semibold text-center text-gray-900 dark:text-gray-100 mb-2">Agentic Workflows</h3>
                <p className="text-base text-center text-gray-700 dark:text-gray-300">Exploring how Agentic AI can be leveraged to automate tasks in the development cycle.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
