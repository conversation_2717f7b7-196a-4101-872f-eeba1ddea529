'use client';

import React from 'react';
import { ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';

export default function ThoughtsPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 font-merriweather">
      {/* Banner Section */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-white dark:text-white mb-4">
              Thoughts
            </h1>
            <p className="text-xl text-white dark:text-white max-w-3xl mx-auto">
              Addressing concerns, sharing further insights, and exploring related ideas.
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Main Content Wrapper - Updated for single card layout with PaperEvaluationV3 styling */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        {/* Apply SectionCard-like styling to this div */}
        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 border border-gray-200 dark:border-gray-700 border-t-0 mb-8 overflow-hidden flex flex-col">
          {/* Gradient bar from SectionCard */}
          <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
          {/* Content padding from SectionCard */}
          <div className="p-6 flex-grow">
            <article className="font-merriweather text-gray-700 dark:text-gray-300">
              {/* Section 1: Addressing AI Concerns */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
                  <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Addressing AI Concerns and My Journey</h2>
                </div>
                <div className="pl-8">
                  <p className="mb-3">
                    I want to address concerns that were raised regarding my usage of AI for this project and its ethical implications. To clarify, when I got up to speed and got invested in the progress of AI development back in 2023, I was mostly interested in its philosophical aspect. Having been a huge science fiction nerd my whole life, seeing what was being made possible with neural networks felt like fiction was becoming reality before my very eyes. I could have imagined some possible scenarios where it could lead us in the future, but these were mostly fueled by what I had seen in books, series, and movies.
                  </p>
                  <p className="mb-3">
                    To find different ideas, I read books such as <a href="https://www.thesingularityisnearer.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">Ray Kurzweil's <em>The Singularity Is Nearer: When We Merge with AI</em></a> and <a href="https://en.wikipedia.org/wiki/Life_3.0" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">Max Tegmark's <em>Life 3.0</em></a>. I thought deeply about the possibility of these scenarios becoming true; they still seemed extremely far-fetched, and I found it hard to believe such events might unfold within my lifetime, if ever.
                  </p>
                </div>
              </div>

              {/* Section 2: Delving Deeper */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
                  <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Delving Deeper: Understanding the Mechanics</h2>
                </div>
                <div className="pl-8">
                  <p className="mb-3">
                    With the current state of progress on one end, and these outlandish futuristic scenarios on the other, I needed to know more about the inner workings of these systems and what had enabled the rapid progression we were seeing at the time. Learning about neural networks by building them from scratch and reading various key papers, such as:
                  </p>
                  <ol className="list-decimal list-inside mb-3 space-y-1">
                    <li><a href="https://home.csulb.edu/~cwallis/382/readings/482/mccolloch.logical.calculus.ideas.1943.pdf" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>A Logical Calculus Of The Ideas Immanent In Nervous Activity</em></a> (Warren S. McCulloch, Walter Pitts, 1943)</li>
                    <li><a href="https://www.ling.upenn.edu/courses/cogs501/Rosenblatt1958.pdf" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>The Perceptron: A Probabilistic Model For Information Storage And Organization In The Brain</em></a> (Frank Rosenblatt, 1958)</li>
                    <li><a href="https://proceedings.neurips.cc/paper_files/paper/2012/file/c399862d3b9d6b76c8436e924a68c45b-Paper.pdf" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>ImageNet Classification with Deep Convolutional Neural Networks</em></a> (Alex Krizhevsky, Ilya Sutskever, Geoffrey Hinton, 2012)</li>
                    <li><a href="https://proceedings.neurips.cc/paper_files/paper/2017/file/3f5ee243547dee91fbd053c1c4a845aa-Paper.pdf" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>Attention Is All You Need</em></a> (Vaswani et al., 2017)</li>
                    <li><a href="https://storage.googleapis.com/deepmind-media/alphago/AlphaGoNaturePaper.pdf" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>Mastering The Game of Go with Deep Neural Networks and Tree Search</em></a> (Google DeepMind et al., 2016)</li>
                    <li><a href="https://www.nature.com/articles/s41586-021-03819-2" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>Highly Accurate Protein Structure Prediction With AlphaFold</em></a> (Google DeepMind et al., 2021)</li>
                    <li><a href="https://arxiv.org/pdf/2501.12948" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>DeepSeek-R1: Incentivizing Reasoning Capability in LLMs via Reinforcement Learning</em></a> (DeepSeek-AI et al., 2025)</li>
                    <li><a href="https://storage.googleapis.com/deepmind-media/DeepMind.com/Blog/alphaevolve-a-gemini-powered-coding-agent-for-designing-advanced-algorithms/AlphaEvolve.pdf" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline"><em>AlphaEvolve: A Coding Agent For Scientific And Algorithmic Discovery</em></a> (Google DeepMind et al., 2025)</li>
                  </ol>
                  <p className="mb-3">
                    Equipped with knowledge from these papers, I had a more complete picture of the current state of progress, what led to it, and where it was most likely to progress. I could now better extrapolate the trendlines of the current progress, and suddenly, those futuristic scenarios seemed less distant.
                  </p>
                </div>
              </div>

              {/* Section 3: Recursive Self-Improvement */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
                  <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">The Specter of Recursive Self-Improvement</h2>
                </div>
                <div className="pl-8">
                  <p className="mb-3">
                    The key driver in all of these scenarios is recursive self-improvement. This is the process in which AI systems become capable of improving their own design and code, iteratively refining themselves to perform better and be smarter. This could theoretically lead to something called the 'intelligence explosion.' This is when a recursive, self-improving model starts becoming more intelligent exponentially, quickly becoming tens to millions of times as smart as the smartest humans in each field (also called the Singularity).
                  </p>
                  <p className="mb-3">
                    Putting aside whether such a thing would even be possible, it would have a large number of unpredictable effects. Based on current advancements, it's possible to plot a route to that moment. I discovered this website, which I believe is a must-read for anyone interested in the matters mentioned in this section: <a href="https://ai-2027.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">https://ai-2027.com/</a>
                  </p>
                  <p className="mb-3">
                    The trajectory predicted on this website explains it in clear terms:
                  </p>
                  <ol className="list-decimal list-inside mb-3 space-y-1">
                    <li>Current Frontier AI R&D is mostly aimed at creating better Agentic Coding Models. These models are rapidly evolving their capability to perform autonomous software engineering tasks for longer periods of time. "This is the worst AI will ever be."</li>
                    <li>As we've seen in recent advancements like Google's AlphaEvolve, when these autonomous models get access to the right environment and tools, they can discover genuine algorithmic discoveries that, in some cases, seemed impossible for humans.</li>
                    <li>We imagine third or fourth-generation AlphaEvolve-like models; they would already be built on top of previous generations of algorithmic improvements and perhaps even architectural paradigm shifts provided by their predecessors.</li>
                    <li>These improvements compound over time. The feedback cycle of improvements gains momentum, with humans being less and less involved in discovering the improvements in AI.</li>
                    <li>As the feedback cycle speeds up, AI becomes not only the best programmer to ever exist but also the best researcher. At this point, AI accelerates and produces more novel science than humanity does.</li>
                  </ol>
                </div>
              </div>

              {/* Section 4: Personal Convictions */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
                  <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Personal Convictions and Future Outlook</h2>
                </div>
                <div className="pl-8">
                  <p className="mb-3">
                    This is a recursive self-improvement scenario, but starting from our current situation. Will it actually turn out that way? As time has passed since my pivot towards AI, it seems that the evidence is mounting in favor of this scenario, and that naysayers are increasingly having to come up with more far-fetched arguments to argue against AI progress and its capabilities.
                  </p>
                  <p className="mb-3">
                    The end result of this scenario can have amazing benefits for humanity (fully automated luxury space communism, UBI, Longevity Escape Velocity), or it could mean the end of the human race (paperclip maximizer, biological warfare, nuclear armageddon), or land us somewhere in between.
                  </p>
                  <p className="mb-3">
                    We can be certain, though, that as we approach more and more capable models, the race between global powers will intensify. As we come closer to realizing the idea of artificial general intelligence (AGI), it will become a geopolitical matter and be akin to the nuclear arms race, with slim to no chance of a slowdown.
                  </p>
                  <p className="mb-3">
                    This means that if we were to assume that this race is inevitable, it would seem futile to me not to engage with the technology and become proficient with it, as the chances of it dictating our future are much larger than it fading away into the background.
                  </p>
                  <p className="mb-3">
                    I personally do think this race is inevitable. I believe, on a philosophical level, that life that reaches our levels of intelligence, anywhere in the universe, is poised to eventually construct similar technology. It could be an answer to Fermi's Paradox or be the Great Filter itself. But it seems that human ingenuity and curiosity will not allow us to sit this one out, and that we will have to see this one through.
                  </p>
                  <p className="mb-3">
                    It gives me a strange feeling when I think about this. During most days, it is not on my mind, and the world seems so normal. When I think about how quickly it could change and how drastically, it gives me a feeling of melancholy, but then as if I were already in this future, reminiscing about the current times.
                  </p>
                </div>
              </div>

              {/* Section 5: Navigating Beliefs */}
              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 mr-2 text-blue-500 shrink-0" />
                  <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Navigating Beliefs and Actions</h2>
                </div>
                <div className="pl-8">
                  <p className="mb-3">
                    Now, this belief obviously influences the way I perceive the future, my role within the world, and how I decide to act within it. One could use this argument (RSI) to justify questionable behavior, as the future would be so unpredictable, and the potential scientific advancements beyond anything we could imagine, that any mistakes made now could be fixed in the future using hyper-advanced technology.
                  </p>
                  <p className="mb-3">
                    Before this raises the suspicion that I personally justify my usage of AI with this argument, we should look at it from a different perspective. It would be foolish to simply put all hope onto hypothetical future benefits to justify all present-day pollution—a techno-optimism trap. I am acutely aware of the emissions caused by the AI sector and their rapid growth and acknowledge that it is a real issue.
                  </p>
                </div>
              </div>
            </article>
          </div>
        </div>
      </div>
    </div>
  );
}
