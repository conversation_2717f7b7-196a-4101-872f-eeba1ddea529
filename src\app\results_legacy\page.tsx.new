'use client';

import React, { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import {
  DocumentTextIcon,
  ExclamationTriangleIcon,
  LightBulbIcon,
  ChartBarIcon,
  AdjustmentsHorizontalIcon,
  HandThumbUpIcon,
  HandThumbDownIcon,
  ScaleIcon,
  BoltIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { dracula } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import { useModelContext } from '@/context/ModelContext';
import CollapsiblePaperCard from '@/components/CollapsiblePaperCard';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { useSearchParams } from 'next/navigation';
import PaperNavigation from '@/components/PaperNavigation';

// Define the structure for the resultsInsights object
interface ResultsInsights {
  claimedOutcomes: string;
  contextualizedBenefits: {
    audioPluginApplications: string;
    problemSolvingPotential: string;
  };
  contextualizedDrawbacks: {
    limitationsForAudio: string;
    implementationHurdles: string;
  };
  feasibilityAssessment: string;
  keyTakeawaysForAudioDev: string;
}

// Define the structure for each entry on the results page
interface ResultEntry {
  paperTitle: string;
  paperSlug: string;
  resultsInsights: ResultsInsights;
}

const ResultsPage = () => {
  const [resultsData, setResultsData] = useState<ResultEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const { selectedModel } = useModelContext();
  const searchParams = useSearchParams();
  const paperParam = searchParams.get('paper');
  
  // If a paper is specified in the URL, set it as the search query
  useEffect(() => {
    if (paperParam) {
      // Convert from slug to display format for searching
      const displayName = paperParam
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      setSearchQuery(displayName);
    }
  }, [paperParam]);
  
  // Filter results based on search query
  const filteredResults = useMemo(() => {
    if (!searchQuery.trim()) return resultsData;
    
    const lowerCaseQuery = searchQuery.toLowerCase();
    
    return resultsData.filter(entry => 
      // Search in paper title
      entry.paperTitle.toLowerCase().includes(lowerCaseQuery) ||
      // Search in claimed outcomes
      (entry.resultsInsights.claimedOutcomes?.toLowerCase().includes(lowerCaseQuery) ?? false) ||
      // Search in benefits
      (entry.resultsInsights.contextualizedBenefits?.audioPluginApplications?.toLowerCase().includes(lowerCaseQuery) ?? false) ||
      (entry.resultsInsights.contextualizedBenefits?.problemSolvingPotential?.toLowerCase().includes(lowerCaseQuery) ?? false) ||
      // Search in drawbacks
      (entry.resultsInsights.contextualizedDrawbacks?.limitationsForAudio?.toLowerCase().includes(lowerCaseQuery) ?? false) ||
      (entry.resultsInsights.contextualizedDrawbacks?.implementationHurdles?.toLowerCase().includes(lowerCaseQuery) ?? false) ||
      // Search in feasibility assessment
      (entry.resultsInsights.feasibilityAssessment?.toLowerCase().includes(lowerCaseQuery) ?? false) ||
      // Search in key takeaways
      (entry.resultsInsights.keyTakeawaysForAudioDev?.toLowerCase().includes(lowerCaseQuery) ?? false)
    );
  }, [resultsData, searchQuery]);

  useEffect(() => {
    async function fetchAllResultsData() {
      setLoading(true);
      setError(null);
      try {
        const responseFilenames = await fetch(`/api/list-evaluations?model=${selectedModel}`);
        if (!responseFilenames.ok) {
          const errorData = await responseFilenames.json();
          throw new Error(`Failed to list evaluation files: ${responseFilenames.status} ${errorData.error || ''}`);
        }
        const data = await responseFilenames.json();
        const files = data.files || [];

        if (files.length === 0) {
          setResultsData([]);
          console.log(`No evaluation files found for results with model: ${selectedModel}`);
          return;
        }

        const allResults: ResultEntry[] = [];
        for (const filePath of files) {
          try {
            // Handle both legacy and new directory structure
            const evaluationPath = filePath.includes('/') 
              ? `/papers/evaluations/${filePath}` 
              : `/papers/evaluations/${filePath}`;

            const responseEvaluation = await fetch(evaluationPath);
            if (!responseEvaluation.ok) {
              console.error(`Failed to fetch evaluation file ${filePath}: ${responseEvaluation.status}`);
              continue;
            }

            // Parse the JSON data
            let evaluationData;
            try {
              const text = await responseEvaluation.text();
              evaluationData = JSON.parse(text);
            } catch (jsonError) {
              console.error(`Failed to parse JSON from evaluation file ${filePath}:`, jsonError);
              continue;
            }
            
            // Extract the paper title and slug from the metadata section
            const metadata = evaluationData.metadata || {};
            const paperTitle = metadata.title || 'Unknown Paper';
            
            // Generate a proper slug for the paper
            let paperSlug = '';
            if (metadata.paperSlug) {
              paperSlug = metadata.paperSlug;
            } else {
              // Extract the paper name from the file path
              const fileName = filePath.replace('.json', '').split('/').pop() || '';
              
              // Convert from 'Adaptive_Temperature_Sampling' to 'adaptive_temperature_sampling'
              paperSlug = fileName
                .replace(/^\d+_/, '') // Remove number prefix like '1_'
                .replace(/Evaluation_(.*)_Gemini$/, '$1') // Remove Evaluation_ prefix and _Gemini suffix
                .toLowerCase();
            }

            // Get the resultsInsights section
            const resultsInsights = evaluationData.resultsInsights;
            
            // Only add entries that have resultsInsights
            if (resultsInsights) {
              allResults.push({
                paperTitle,
                paperSlug,
                resultsInsights
              });
            } else {
              if (!paperTitle) {
                console.warn(`Skipping results for ${filePath}: Missing title.`);
              } else {
                console.warn(`Skipping results for ${filePath}: Missing 'resultsInsights' object.`);
              }
            }
          } catch (fileError: Error | unknown) {
            const errorMessage = fileError instanceof Error ? fileError.message : 'Unknown error';
            console.error(`Error processing file ${filePath} for results:`, errorMessage);
          }
        }
        setResultsData(allResults);

      } catch (e: Error | unknown) {
        const errorMessage = e instanceof Error ? e.message : 'Unknown error';
        setError(`Failed to load results data: ${errorMessage}`);
        console.error(e);
      } finally {
        setLoading(false);
      }
    }

    fetchAllResultsData();
  }, [selectedModel]); // Re-fetch when selected model changes

  // Markdown renderer component with consistent styling
  const MarkdownRenderer = ({ content }: { content: string | undefined }) => {
    if (!content) return null;
    return (
      <ReactMarkdown
        components={{
          // @ts-expect-error - ReactMarkdown component types are complex
          code: ({ inline, className, children, ...props }) => {
            const match = /language-(\w+)/.exec(className || '');
            return !inline && match ? (
              <SyntaxHighlighter
                // @ts-expect-error - Style type issue with SyntaxHighlighter
                style={dracula}
                language={match[1]}
                PreTag="div"
                {...props}
              >
                {String(children).replace(/\n$/, '')}
              </SyntaxHighlighter>
            ) : (
              <code className={className} {...props}>
                {children}
              </code>
            );
          },
          p: (props) => <p className="text-gray-700 dark:text-gray-300 mb-4" {...props} />,
          ul: (props) => <ul className="list-disc pl-5 mb-4 text-gray-700 dark:text-gray-300" {...props} />,
          ol: (props) => <ol className="list-decimal pl-5 mb-4 text-gray-700 dark:text-gray-300" {...props} />,
          li: (props) => <li className="mb-1" {...props} />,
          strong: (props) => <strong className="font-semibold text-gray-800 dark:text-gray-200" {...props} />
        }}
      >
        {content}
      </ReactMarkdown>
    );
  };

  // Helper function to render key-value pairs with icons
  const renderKeyValue = (key: string, value: string | undefined, IconComponent?: React.ElementType, indent = false, iconColorClass = "text-gray-700 dark:text-gray-300") => {
    if (!value) return null;
    
    // Ensure value is a string
    const stringValue = typeof value === 'string' ? value : String(value);
    
    // Handle the case where value might be a comma-separated list or have special formatting
    let processedValue = stringValue;
    
    // Special handling for Key Takeaways for Audio Dev
    if (key === 'Key Takeaways for Audio Dev') {
      // Check if the value is a comma-separated list
      if (stringValue.includes(',')) {
        // Split by commas, trim each item, and format as a list
        const items = stringValue.split(',').map(item => item.trim());
        processedValue = items.map(item => `- ${item}`).join('\n');
      } else if (stringValue.includes('\n')) {
        // If it's already line-separated, ensure each line starts with a bullet
        const lines = stringValue.split('\n').map(line => line.trim());
        processedValue = lines.map(line => line.startsWith('-') ? line : `- ${line}`).join('\n');
      } else {
        processedValue = stringValue;
      }
    }
    
    return (
      <div className={`${indent ? 'ml-8' : ''} mb-4`}>
        <div className="flex items-start">
          {IconComponent && (
            <IconComponent className={`h-5 w-5 mr-2 mt-1 flex-shrink-0 ${iconColorClass}`} />
          )}
          <div>
            <h4 className="font-semibold text-gray-800 dark:text-gray-200">{key}</h4>
            <MarkdownRenderer content={processedValue} />
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gradient-to-br from-gray-100 to-blue-100 dark:from-zinc-950 dark:to-slate-900 text-gray-900 dark:text-gray-100 p-4">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-4 border-gray-300 dark:border-gray-600 border-t-sky-500 dark:border-t-sky-400 mb-4"></div>
          <h2 className="text-2xl font-bold mb-2">Loading Results</h2>
          <p className="text-gray-700 dark:text-gray-300">Fetching results insights from evaluation files...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gradient-to-br from-gray-100 to-red-100 dark:from-zinc-950 dark:to-red-950 text-gray-900 dark:text-gray-100 p-4">
        <div className="text-center max-w-xl">
          <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-2">Error Loading Results</h2>
          <p>{error}</p>
          <Link href="/" className="mt-6 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-lg font-medium transition-colors">
            Go Back Home
          </Link>
        </div>
      </div>
    );
  }

  if (resultsData.length === 0) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-gradient-to-br from-gray-100 to-blue-100 dark:from-zinc-950 dark:to-slate-900 text-gray-900 dark:text-gray-100 p-4">
        <DocumentTextIcon className="h-12 w-12 mb-4 text-gray-500 dark:text-gray-400" />
        <h2 className="text-2xl font-bold mb-2">No Results Insights Found</h2>
        <p className="text-gray-700 dark:text-gray-300">There are currently no evaluation files with results insights.</p>
        <Link href="/" className="mt-6 px-4 py-2 bg-sky-600 hover:bg-sky-700 text-white rounded-md text-lg font-medium transition-colors">
          Go Back Home
        </Link>
      </div>
    );
  }
  
  // Find the selected paper if a paper parameter is provided
  const selectedPaper = paperParam && filteredResults.find(entry => entry.paperSlug === paperParam);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-black">
      {/* Paper Navigation - only show when a specific paper is selected */}
      {selectedPaper && (
        <PaperNavigation paperSlug={selectedPaper.paperSlug} paperTitle={selectedPaper.paperTitle} />
      )}
      
      <div className="container mx-auto px-4 py-12">
        <header className="mb-12 text-center">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-6">Results & Insights</h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Critical analysis of outcomes, benefits, drawbacks, and feasibility from evaluated research papers, contextualized for audio plugin development.
          </p>
        </header>

        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">Research Results Analysis</h2>
          
          {/* Search and content area */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
            {/* Search bar */}
            <div className="mb-6">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 text-sm"
                  placeholder="Search results by title, benefits, limitations, or key takeaways..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              {searchQuery && (
                <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Found {filteredResults.length} papers matching your search
                </div>
              )}
            </div>

            {/* Loading and error states */}
            {loading && (
              <div className="py-8 text-center">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 dark:border-gray-600 border-t-sky-500 dark:border-t-sky-400"></div>
                <p className="mt-2 text-gray-600 dark:text-gray-400">Loading results...</p>
              </div>
            )}

            {error && (
              <div className="py-8 text-center">
                <ExclamationTriangleIcon className="h-8 w-8 text-red-500 mx-auto mb-2" />
                <p className="text-red-600 dark:text-red-400">{error}</p>
              </div>
            )}

            {/* Empty state */}
            {!loading && !error && resultsData.length === 0 && (
              <div className="py-8 text-center">
                <p className="text-gray-600 dark:text-gray-300">No results data found. Please check your evaluation files.</p>
              </div>
            )}

            {/* Content - Paper Cards with Results */}
            {!loading && !error && filteredResults.length > 0 && (
              <div className="space-y-4">
                {filteredResults.map((entry, index) => (
                  <CollapsiblePaperCard 
                    key={entry.paperSlug + index} 
                    title={entry.paperTitle} 
                    slug={entry.paperSlug}
                    defaultExpanded={filteredResults.length === 1} // Auto-expand if only one result
                  >
                    <div className="space-y-4">
                      {/* Claimed Outcomes */}
                      {renderKeyValue('Claimed Outcomes', entry.resultsInsights.claimedOutcomes, ChartBarIcon, false, "text-blue-500 dark:text-blue-400")}
                      
                      {/* Benefits Section */}
                      <div className="pt-2">
                        <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-200 mb-2 flex items-center">
                          <HandThumbUpIcon className="h-5 w-5 mr-2 text-green-500 flex-shrink-0" /> Contextualized Benefits
                        </h3>
                        <div className="bg-green-50 dark:bg-green-900/10 rounded-md p-3 border border-green-100 dark:border-green-800/30">
                          {renderKeyValue('Audio Plugin Applications', entry.resultsInsights.contextualizedBenefits?.audioPluginApplications, BoltIcon, true, "text-green-500 dark:text-green-400")}
                          {renderKeyValue('Problem Solving Potential', entry.resultsInsights.contextualizedBenefits?.problemSolvingPotential, QuestionMarkCircleIcon, true, "text-green-500 dark:text-green-400")}
                        </div>
                      </div>

                      {/* Drawbacks Section */}
                      <div className="pt-2">
                        <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-200 mb-2 flex items-center">
                          <HandThumbDownIcon className="h-5 w-5 mr-2 text-red-500 flex-shrink-0" /> Contextualized Drawbacks
                        </h3>
                        <div className="bg-red-50 dark:bg-red-900/10 rounded-md p-3 border border-red-100 dark:border-red-800/30">
                          {renderKeyValue('Limitations for Audio', entry.resultsInsights.contextualizedDrawbacks?.limitationsForAudio, ExclamationTriangleIcon, true, "text-red-500 dark:text-red-400")}
                          {renderKeyValue('Implementation Hurdles', entry.resultsInsights.contextualizedDrawbacks?.implementationHurdles, AdjustmentsHorizontalIcon, true, "text-red-500 dark:text-red-400")}
                        </div>
                      </div>

                      {/* Assessment and Takeaways */}
                      <div className="pt-2">
                        {renderKeyValue('Feasibility Assessment', entry.resultsInsights.feasibilityAssessment, ScaleIcon, false, "text-purple-500 dark:text-purple-400")}
                      </div>
                      
                      <div className="pt-2 bg-yellow-50 dark:bg-yellow-900/10 rounded-md p-3 border border-yellow-100 dark:border-yellow-800/30">
                        {renderKeyValue('Key Takeaways for Audio Dev', entry.resultsInsights.keyTakeawaysForAudioDev, LightBulbIcon, false, "text-yellow-500 dark:text-yellow-400")}
                      </div>
                    </div>
                  </CollapsiblePaperCard>
                ))}
              </div>
            )}
          </div>
        </section>
        
        <footer className="mt-16 pt-8 border-t border-gray-200 dark:border-zinc-700/80 text-center text-gray-500 dark:text-gray-400">
          <p>&copy; {new Date().getFullYear()} Thesis Narrative Site. All insights dynamically loaded.</p>
        </footer>
      </div>
    </div>
  );
};

export default ResultsPage;
