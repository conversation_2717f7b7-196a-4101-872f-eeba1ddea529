'use client';

import React from 'react';

interface CardData {
  title: string;
  content: string | React.ReactNode;
}

export default function EthicsPage() {
  const ethicsCardData: CardData[] = [
    {
      title: "Responsible AI Usage and Transparency",
      content: "Detailed account of how AI tools were employed responsibly. Discussion on the transparency of methods, data sources used for training models (if known), and limitations of the AI."
    },
    {
      title: "Data Privacy and Security Considerations",
      content: "Explanation of measures taken to ensure data privacy if any personal or sensitive data was handled. Discussion of data security practices during the research."
    },
    {
      title: "Intellectual Property and Fair Use",
      content: "Addressing how intellectual property rights were respected, including proper citation, adherence to licensing for any tools or datasets, and ensuring fair use of materials."
    },
    {
      title: "Mitigating Bias in AI and Analysis",
      content: "Awareness and discussion of potential biases in the AI models used or in the interpretation of their outputs. Steps taken to mitigate these biases and ensure a fair analysis."
    },
    {
      title: "Academic Integrity and Originality",
      content: "Commitment to academic integrity, ensuring all work is original, and that AI tools were used as aids for research and analysis, not for generating final content without critical oversight."
    },
    {
      title: "Broader Societal and Ethical Implications",
      content: "Reflection on the wider ethical implications of the research topic and the use of AI in this domain. Consideration of potential positive and negative societal impacts."
    }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 font-merriweather">
      {/* Banner Section */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-white dark:text-white mb-4">
              Ethics
            </h1>
            <p className="text-xl text-white dark:text-white max-w-3xl mx-auto">
              Discussing the ethical considerations related to the research and the use of AI tools.
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Main Content Wrapper with Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
          {ethicsCardData.map((card, index) => (
            <div key={index} className="bg-white dark:bg-zinc-800 rounded-lg shadow-lg overflow-hidden flex flex-col">
              <div className="h-1.5 bg-gradient-to-r from-blue-500 to-purple-600"></div>
              <div className="p-6 flex-grow">
                <h4 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">
                  {card.title}
                </h4>
                {typeof card.content === 'string' ? (
                  <p className="text-gray-700 dark:text-gray-300 text-sm">
                    {card.content}
                  </p>
                ) : (
                  <div className="text-gray-700 dark:text-gray-300 text-sm">
                    {card.content}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Concluding article section */}
        <article className="prose prose-lg dark:prose-invert max-w-none mx-auto mt-12">
          <h2 className="text-2xl font-semibold mt-8 mb-4">Ethical Framework Summary</h2>
          <p>
            (A brief summary that synthesizes the key ethical considerations discussed in the cards and reiterates the commitment to ethical research practices...)
          </p>
        </article>

      </div>
    </div>
  );
}
