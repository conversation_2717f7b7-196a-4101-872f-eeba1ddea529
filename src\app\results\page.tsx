'use client';

import React from 'react';

interface CardData {
  title: string;
  content: string | React.ReactNode;
}

export default function ResultsPage() {
  const resultsCardData: CardData[] = [
    {
      title: "Summary of Key Findings",
      content: (
        <>
          <p className="mb-3">In this page we describe the outcome of the research conducted in attempt to answer my research question: "How do I design, build and use system(s) that discover, assess, and integrate emerging AI research, methodologies and approaches into my audio software development workflow?" </p>
          <p className="mb-3">The system currently provides a solid alternative to manually doing the first round of researching existing academic literature. It allows uploading of papers and using LLMs to give its judement based upon user defined criteria. You can simply look at the score and decide wether its worth your time to read the evaluation.  And if so go deeper until you are convinced enough to read the original paper. It effectively saves time and will pay equal amounts of attention to any paper given bypassing some human flaws such as that we limited focus, motivation and drive to do these kind of tasks, as they are very demanding for most people.</p>
          <p className="mb-3">I now have designed, built and operated the first system to aid me with staying up to date with AI related academic publications. whilst being a work in progress and deserving of more improvements after this graduation period it is already giving me useful insights and creating a environment for me which motivates me to learn. For this version i used a fixed corpus of 21 research papers, which by now are probably outdated. The point being that this application is to showcase the proof of concept that this is effective strategy and saves me time and energy. </p>
          <p className="mb-3">The main result is me entering in a prolonged process of trial and error in finding the right structures and methods with the goal of obtaining more insightful results from the evaluated papers. With an idea organically developing by looking for a AI assisted approach for my critical review to realizing it was essentialy becoming an automated research tool that could assist me for some time in the future and possibly even others. </p>
          <p className="mb-3">After iterating and landing on this current version, its outputs are much more closely aligned with the tasks and work that I perform on a daily basis, increasing its relevance and usefulness for real world application.  </p>
          <p className="mb-3">We end up with a dynamic system that, once set up correctly performs a thorough analysis of any source material provided and presenting us with its findings that are contextulaized to our specified goals and interests.  </p>
          <p className="mb-3">We see that with the proper scaffolding, current era LLMs can be leveraged to perform this task for us. And that even though these models can hallucinate or make mistakes. This does not make or break our concept, as the information provided only serves as a intermediary step for us to asses a papers potential and that we should aways manually read papers if we are looking to extract any methods or techniques from them. </p>
          <p className="mb-3">We also observed that during the time that work on this supportive narrative was started and completed that the capabilities of LLMs had increased. Allowing for new approaches not only by our own design but also by their increased capability. Its juggling these factors which turns out is the key element to having a succesful system. </p>
        </>
      )
    },
    {
      title: "Detailed Analysis: Answering the Research Question",
      content: (
        <>
          <h5 className="font-semibold mt-4 mb-2 text-gray-800 dark:text-gray-200">Designing the System</h5>
          <h5 className="font-semibold mt-4 mb-2 text-gray-800 dark:text-gray-200">Building the System</h5>
          <h5 className="font-semibold mt-4 mb-2 text-gray-800 dark:text-gray-200">Using the System</h5>
        </>
      )
    },
    {
      title: "Broader Implications, Limitations, and Unexpected Insights",
      content: (
        <>
          <h5 className="font-semibold mb-2 text-gray-800 dark:text-gray-200">Implications</h5>
          
          <h5 className="font-semibold mt-4 mb-2 text-gray-800 dark:text-gray-200">Limitations</h5>
          
          <h5 className="font-semibold mt-4 mb-2 text-gray-800 dark:text-gray-200">Unexpected Insights and Key Learnings</h5>
        </>
      )
    }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 font-merriweather">
      {/* Banner Section */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-white dark:text-white mb-4">
              Results
            </h1>
            <p className="text-xl text-white dark:text-white max-w-3xl mx-auto">
              Describing the outcomes of the LLM aided research and how it answered the research question.
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Main Content Wrapper with Vertically Stacked Cards */}
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        <div className="space-y-8">
          {resultsCardData.map((card, index) => (
            <div key={index} className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg overflow-hidden flex flex-col">
              <div className="h-1.5 bg-gradient-to-r from-blue-500 to-purple-600"></div>
              <div className="p-6 md:p-8 flex-grow">
                <h4 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  {card.title}
                </h4>
                {typeof card.content === 'string' ? (
                  <p className="text-gray-700 dark:text-gray-300">
                    {card.content}
                  </p>
                ) : (
                  <div className="text-gray-700 dark:text-gray-300 prose prose-sm sm:prose dark:prose-invert max-w-none">
                    {card.content}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
