'use client';

import React, { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import {
  DocumentTextIcon,
  ExclamationTriangleIcon,
  LightBulbIcon,
  ChartBarIcon,
  AdjustmentsHorizontalIcon,
  HandThumbUpIcon,
  HandThumbDownIcon,
  ScaleIcon,
  BoltIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { dracula } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import { useModelContext } from '@/context/ModelContext';
import CollapsiblePaperCard from '@/components/CollapsiblePaperCard';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { useSearchParams } from 'next/navigation';
import PaperNavigation from '@/components/PaperNavigation';
import GlossaryEnhancedContent from '@/components/GlossaryEnhancedContent';

// Define the structure for the resultsInsights object
interface ResultsInsights {
  claimedOutcomes: string;
  contextualizedBenefits: {
    audioPluginApplications: string;
    problemSolvingPotential: string;
  };
  contextualizedDrawbacks: {
    limitationsForAudio: string;
    implementationHurdles: string;
  };
  feasibilityAssessment: string;
  keyTakeawaysForAudioDev: string;
}

// Define the structure for each entry on the results page
interface ResultEntry {
  paperTitle: string;
  paperSlug: string;
  resultsInsights: ResultsInsights;
}

const ResultsPage = () => {
  const [resultsData, setResultsData] = useState<ResultEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedCards, setExpandedCards] = useState<Set<string>>(new Set());
  const [storedPaperTitle, setStoredPaperTitle] = useState<string | undefined>(undefined);
  const { selectedModel } = useModelContext();
  const searchParams = useSearchParams();
  const paperParam = searchParams.get('paper');

  // If a paper is specified in the URL, set it as the search query
  // Also retrieve the stored paper title from localStorage (client-side only)
  useEffect(() => {
    if (paperParam) {
      // Convert from slug to display format for searching
      const displayName = paperParam
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      setSearchQuery(displayName);

      // Get stored paper title from localStorage (client-side only)
      const storedTitle = localStorage.getItem('currentPaperTitle');
      if (storedTitle) {
        setStoredPaperTitle(storedTitle);
      }
    }
  }, [paperParam]);

  // Initialize expanded cards if a paper is specified in the URL
  useEffect(() => {
    if (paperParam) {
      // Always set the paper parameter in the expanded cards set when it's present in the URL
      // This ensures the card will be expanded when accessed via the mini-menu
      setExpandedCards(new Set([paperParam]));
      console.log('Results page - Setting expanded card for paper:', paperParam);
    }
  }, [paperParam]);

  // Helper function to safely search in string or array values
  const safeSearch = (value: string | string[] | undefined, query: string): boolean => {
    if (!value) return false;

    if (Array.isArray(value)) {
      // If it's an array, check if any item includes the query
      return value.some(item => item.toLowerCase().includes(query));
    } else {
      // If it's a string, check if it includes the query
      return value.toLowerCase().includes(query);
    }
  };

  // Filter results based on search query
  const filteredResults = useMemo(() => {
    if (!searchQuery.trim()) return resultsData;

    const lowerCaseQuery = searchQuery.toLowerCase();

    return resultsData.filter(entry =>
      // Search in paper title
      entry.paperTitle.toLowerCase().includes(lowerCaseQuery) ||
      // Search in claimed outcomes
      safeSearch(entry.resultsInsights.claimedOutcomes, lowerCaseQuery) ||
      // Search in benefits
      safeSearch(entry.resultsInsights.contextualizedBenefits?.audioPluginApplications, lowerCaseQuery) ||
      safeSearch(entry.resultsInsights.contextualizedBenefits?.problemSolvingPotential, lowerCaseQuery) ||
      // Search in drawbacks
      safeSearch(entry.resultsInsights.contextualizedDrawbacks?.limitationsForAudio, lowerCaseQuery) ||
      safeSearch(entry.resultsInsights.contextualizedDrawbacks?.implementationHurdles, lowerCaseQuery) ||
      // Search in feasibility assessment
      safeSearch(entry.resultsInsights.feasibilityAssessment, lowerCaseQuery) ||
      // Search in key takeaways
      safeSearch(entry.resultsInsights.keyTakeawaysForAudioDev, lowerCaseQuery)
    );
  }, [resultsData, searchQuery]);

  useEffect(() => {
    async function fetchAllResultsData() {
      setLoading(true);
      setError(null);
      try {
        const responseFilenames = await fetch(`/api/list-evaluations?model=${selectedModel}`);
        if (!responseFilenames.ok) {
          const errorData = await responseFilenames.json();
          throw new Error(`Failed to list evaluation files: ${responseFilenames.status} ${errorData.error || ''}`);
        }
        const data = await responseFilenames.json();
        const files = data.files || [];

        if (files.length === 0) {
          setResultsData([]);
          console.log(`No evaluation files found for results with model: ${selectedModel}`);
          return;
        }

        const allResults: ResultEntry[] = [];
        for (const filePath of files) {
          try {
            // Handle both legacy and new directory structure
            const evaluationPath = filePath.includes('/')
              ? `/papers/evaluations/${filePath}`
              : `/papers/evaluations/${filePath}`;

            const responseEvaluation = await fetch(evaluationPath);
            if (!responseEvaluation.ok) {
              console.warn(`Failed to fetch ${filePath} for results: ${responseEvaluation.status}`);
              continue;
            }

            // Handle empty or invalid JSON files
            let evaluationData;
            try {
              const text = await responseEvaluation.text();
              if (!text || text.trim() === '') {
                console.warn(`File ${filePath} for results is empty`);
                continue; // Skip empty files
              }
              evaluationData = JSON.parse(text);
            } catch (parseError) {
              console.error(`Error processing file ${filePath} for results:`, parseError);
              continue; // Skip files with parsing errors
            }

            const paperTitle = evaluationData.metadata?.title;
            // Extract paper slug from the filename
            const filename = filePath.includes('/')
              ? filePath.split('/')[1] // New structure: folder/filename.json
              : filePath; // Legacy structure: filename.json

            const paperSlug = filename
              .replace(/^Evaluation_/, '')
              .replace(/_o3\.json$/, '')
              .replace(/_Gemini\.json$/, '')
              .replace(/_Sonnet\.json$/, '')
              .replace(/\.json$/, '');

            // Only add entries that have resultsInsights
            if (evaluationData.resultsInsights) {
              allResults.push({
                paperTitle: paperTitle || 'Unknown Paper',
                paperSlug,
                resultsInsights: evaluationData.resultsInsights
              });
            } else {
              console.warn(`Skipping ${filePath} for results: Missing resultsInsights`);
            }
          } catch (fileError) {
            console.error(`Error processing file ${filePath} for results:`, fileError);
          }
        }

        setResultsData(allResults);
      } catch (e) {
        const errorMessage = e instanceof Error ? e.message : 'Unknown error';
        setError(`Failed to load results data: ${errorMessage}`);
        console.error(e);
      } finally {
        setLoading(false);
      }
    }
    fetchAllResultsData();
  }, [selectedModel]); // Re-fetch when selected model changes

  const MarkdownRenderer = ({ content: initialContent }: { content: string | undefined }) => {
    // Ensure contentToRender is always a string. Default to empty string if initialContent is not a string.
    const contentToRender: string = typeof initialContent === 'string' ? initialContent : "";

    // Log the original problematic content if it was a string and matched the criteria
    if (typeof initialContent === 'string' && initialContent.startsWith("1. **Dynamic Temperature Can Improve C++ Generation:**")) {
      console.log("MarkdownRenderer received problematic content (original):", JSON.stringify(initialContent));
    }

    // If the original content was not a string (and thus contentToRender is now ""),
    // and we want to avoid rendering an empty ReactMarkdown component, we could return null here.
    // However, to satisfy the previous error path and ensure ReactMarkdown always gets a string,
    // we'll proceed with contentToRender (which might be "").
    // if (typeof initialContent !== 'string') {
    //   return null; // Or, if an empty string render is acceptable, remove this block.
    // }

    return (
      <GlossaryEnhancedContent
        content={contentToRender}
        className="prose dark:prose-invert max-w-none [&_p]:text-gray-700 [&_p]:dark:text-gray-300 [&_p]:mb-4 [&_ul]:list-disc [&_ul]:pl-5 [&_ul]:mb-4 [&_ul]:text-gray-700 [&_ul]:dark:text-gray-300 [&_ol]:list-decimal [&_ol]:pl-5 [&_ol]:mb-4 [&_ol]:text-gray-700 [&_ol]:dark:text-gray-300 [&_li]:mb-1 [&_li]:text-gray-700 [&_li]:dark:text-gray-300 [&_strong]:font-semibold [&_strong]:text-gray-800 [&_strong]:dark:text-gray-100"
      />
    );
  };

  // Helper function to render key-value pairs with optional icons and markdown support
  const renderKeyValue = (key: string, value: string | undefined, IconComponent?: React.ElementType, indent = false, iconColorClass = "text-gray-700 dark:text-gray-300") => {
    if (!value) return null;

    const contentClass = `text-sm text-gray-700 dark:text-gray-300 leading-relaxed ${indent ? 'ml-7' : ''}`;

    return (
      <div className={`mb-3 ${indent ? 'pl-4 border-l-2 border-gray-200 dark:border-gray-700' : ''}`}>
        <h4 className={`text-md font-semibold text-gray-800 dark:text-gray-200 mb-1 flex items-center ${iconColorClass}`}>
          {IconComponent && <IconComponent className={`h-4 w-4 mr-2 flex-shrink-0 ${iconColorClass}`} />}
          {key}
        </h4>
        {/* Always wrap MarkdownRenderer in a div to avoid p-in-p and simplify logic */}
        <div className={contentClass}>
          <MarkdownRenderer content={value} />
        </div>
      </div>
    );
  };

  // Handler for card expansion state changes
  const handleCardExpandChange = (expanded: boolean, slug: string) => {
    console.log(`Results page - Card expansion change: ${expanded ? 'expanded' : 'collapsed'} for ${slug}`);
    setExpandedCards(prevExpandedCards => {
      const newExpandedCards = new Set(prevExpandedCards);
      if (expanded) {
        newExpandedCards.add(slug);

        // Store this as the last visited paper in localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('lastVisitedPaper', slug);

          // Also store the paper title if we have it
          const paperEntry = resultsData.find(entry => entry.paperSlug === slug);
          if (paperEntry) {
            localStorage.setItem('currentPaperTitle', paperEntry.paperTitle);
          }
        }
      } else {
        newExpandedCards.delete(slug);
      }
      console.log('Results page - Expanded cards after change:', Array.from(newExpandedCards));
      return newExpandedCards;
    });
  };

  // The useEffect hook for expanded cards has been moved to the top of the component

  // Find the currently expanded paper if any
  const expandedPaperSlug = expandedCards.size > 0 ? Array.from(expandedCards)[0] : null;
  const expandedPaper = expandedPaperSlug && filteredResults.find(entry => entry.paperSlug === expandedPaperSlug);

  // Find the selected paper if a paper parameter is provided
  const selectedPaper = paperParam && filteredResults.find(entry => entry.paperSlug === paperParam);

  // Determine which paper to show in the navigation menu
  const paperToShow = expandedPaper || selectedPaper;

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gradient-to-br from-gray-100 to-blue-100 dark:from-zinc-950 dark:to-slate-900 text-gray-900 dark:text-gray-100 p-4">
        <div className="animate-pulse text-xl font-semibold">Loading Results Insights...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300 p-4">
        <ExclamationTriangleIcon className="h-12 w-12 mb-4" />
        <h2 className="text-2xl font-bold mb-2">Error Loading Results</h2>
        <p>{error}</p>
        <Link href="/" className="mt-6 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-lg font-medium transition-colors">
          Go Back Home
        </Link>
      </div>
    );
  }

  if (resultsData.length === 0) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-gray-100 dark:bg-gray-900 text-gray-700 dark:text-gray-300 p-4">
        <DocumentTextIcon className="h-12 w-12 mb-4 text-gray-500" />
        <h2 className="text-2xl font-bold mb-2">No Results Found</h2>
        <p>No results data available for the selected model.</p>
        <Link href="/" className="mt-6 px-4 py-2 bg-sky-600 hover:bg-sky-700 text-white rounded-md text-lg font-medium transition-colors">
          Go Back Home
        </Link>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-black font-roboto">
      {/* Banner Section - Copied from Source Material/Introduction */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>

        {/* Content Container */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-black dark:text-white mb-4">
              Results & Insights
            </h1>
            <p className="text-xl text-black dark:text-white max-w-3xl mx-auto">
              Contextualized findings and practical implications derived from the analyzed research.
            </p>
          </div>
        </div>

        {/* Bottom Gradient Bar */}
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Main Content Wrapper - Constrained Width */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Paper Navigation (if applicable) - MOVED HERE */}
        {(paperParam || expandedCards.size > 0) && filteredResults.length > 0 && (
          <div className="mb-8">
            <PaperNavigation
              paperSlug={paperToShow ? paperToShow.paperSlug : (paperParam || '')}
              paperTitle={paperToShow ? paperToShow.paperTitle : storedPaperTitle}
            />
          </div>
        )}

        {/* Search Bar - MOVED HERE */}
        <div className="mb-8">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 dark:text-gray-500" aria-hidden="true" />
            </div>
            <input
              type="text"
              placeholder={`Search results... (e.g., paper title, keyword)`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 dark:border-zinc-700 rounded-lg shadow-sm leading-5 bg-white dark:bg-zinc-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-150"
            />
          </div>
        </div>

        {/* Loading and Error States */}
        {loading && (
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 dark:border-zinc-700 border-t-sky-500 dark:border-t-sky-400"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">Loading results...</p>
          </div>
        )}

        {error && (
          <div className="py-8 text-center">
            <p className="text-red-500 dark:text-red-400">{error}</p>
          </div>
        )}

        {/* Empty state */}
        {!loading && !error && resultsData.length === 0 && (
          <div className="py-8 text-center">
            <p className="text-gray-600 dark:text-gray-300">No results data found. Please check your evaluation files.</p>
          </div>
        )}

        {/* Content - Paper Cards with Results */}
        {!loading && !error && filteredResults.length > 0 && (
          <div className="space-y-8">
            {filteredResults.map((entry, index) => {
              // Log the data for Key Takeaways before rendering the card
              if (entry.resultsInsights && entry.resultsInsights.keyTakeawaysForAudioDev) {
                console.log(`Data for Key Takeaways (${entry.paperSlug}):`, entry.resultsInsights.keyTakeawaysForAudioDev, typeof entry.resultsInsights.keyTakeawaysForAudioDev);
              }
              return (
                <CollapsiblePaperCard
                  key={entry.paperSlug + index}
                  title={entry.paperTitle}
                  titleClassName="text-lg md:text-xl"
                  slug={entry.paperSlug}
                  defaultExpanded={!!paperParam && (
                    // Check for exact match
                    paperParam === entry.paperSlug ||
                    // Check for case-insensitive match
                    paperParam.toLowerCase() === entry.paperSlug.toLowerCase() ||
                    // Check if paper slug contains the parameter
                    entry.paperSlug.toLowerCase().includes(paperParam.toLowerCase()) ||
                    // Check if parameter contains the paper slug
                    paperParam.toLowerCase().includes(entry.paperSlug.toLowerCase())
                  )}
                  onExpandChange={(expanded, slug) => handleCardExpandChange(expanded, entry.paperSlug)}
                >
                  <div className="space-y-4">
                    {/* Claimed Outcomes */}
                    {renderKeyValue('Claimed Outcomes', entry.resultsInsights.claimedOutcomes, ChartBarIcon, false, "text-blue-500 dark:text-blue-400")}

                    {/* Benefits Section */}
                    <div className="pt-2">
                      <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-200 mb-2 flex items-center">
                        <HandThumbUpIcon className="h-5 w-5 mr-2 text-green-500 flex-shrink-0" /> Contextualized Benefits
                      </h3>
                      <div className="bg-green-50 dark:bg-green-900/10 rounded-md p-3 border border-green-100 dark:border-green-800/30">
                        {renderKeyValue('Audio Plugin Applications', entry.resultsInsights.contextualizedBenefits?.audioPluginApplications, BoltIcon, true, "text-green-500 dark:text-green-400")}
                        {renderKeyValue('Problem Solving Potential', entry.resultsInsights.contextualizedBenefits?.problemSolvingPotential, QuestionMarkCircleIcon, true, "text-green-500 dark:text-green-400")}
                      </div>
                    </div>

                    {/* Drawbacks Section */}
                    <div className="pt-2">
                      <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-200 mb-2 flex items-center">
                        <HandThumbDownIcon className="h-5 w-5 mr-2 text-red-500 flex-shrink-0" /> Contextualized Drawbacks
                      </h3>
                      <div className="bg-red-50 dark:bg-red-900/10 rounded-md p-3 border border-red-100 dark:border-red-800/30">
                        {renderKeyValue('Limitations for Audio', entry.resultsInsights.contextualizedDrawbacks?.limitationsForAudio, ExclamationTriangleIcon, true, "text-red-500 dark:text-red-400")}
                        {renderKeyValue('Implementation Hurdles', entry.resultsInsights.contextualizedDrawbacks?.implementationHurdles, AdjustmentsHorizontalIcon, true, "text-red-500 dark:text-red-400")}
                      </div>
                    </div>

                    {/* Assessment and Takeaways */}
                    <div className="pt-2">
                      {renderKeyValue('Feasibility Assessment', entry.resultsInsights.feasibilityAssessment, ScaleIcon, false, "text-purple-500 dark:text-purple-400")}
                    </div>

                    <div className="pt-2 bg-yellow-50 dark:bg-yellow-900/10 rounded-md p-3 border border-yellow-100 dark:border-yellow-800/30">
                      {renderKeyValue('Key Takeaways for Audio Dev', entry.resultsInsights.keyTakeawaysForAudioDev, LightBulbIcon, false, "text-yellow-500 dark:text-yellow-400")}
                    </div>
                  </div>
                </CollapsiblePaperCard>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default ResultsPage;
