import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import InlineGlossaryText from '@/components/InlineGlossaryText';

interface MarkdownViewerProps {
  content: string;
}

// Helper function to extract text content from React children
function extractTextFromChildren(children: React.ReactNode): string {
  if (typeof children === 'string') {
    return children;
  }
  if (typeof children === 'number') {
    return String(children);
  }
  if (Array.isArray(children)) {
    return children.map(extractTextFromChildren).join('');
  }
  if (React.isValidElement(children)) {
    return extractTextFromChildren((children.props as any).children);
  }
  return '';
}

export default function MarkdownViewer({ content }: MarkdownViewerProps) {
  return (
    <div className="prose prose-sm dark:prose-invert max-w-none text-gray-700 dark:text-gray-300 leading-relaxed">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          p: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <p className="mb-4" {...props}>
                <InlineGlossaryText text={textContent} />
              </p>
            );
          },
          strong: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <strong className="font-semibold text-gray-900 dark:text-gray-100" {...props}>
                <InlineGlossaryText text={textContent} />
              </strong>
            );
          },
          a: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <a className="text-sky-600 dark:text-sky-400 hover:underline" {...props}>
                <InlineGlossaryText text={textContent} />
              </a>
            );
          },
          li: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <li {...props}>
                <InlineGlossaryText text={textContent} />
              </li>
            );
          },
          h1: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <h1 {...props}>
                <InlineGlossaryText text={textContent} />
              </h1>
            );
          },
          h2: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <h2 {...props}>
                <InlineGlossaryText text={textContent} />
              </h2>
            );
          },
          h3: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <h3 {...props}>
                <InlineGlossaryText text={textContent} />
              </h3>
            );
          },
          h4: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <h4 {...props}>
                <InlineGlossaryText text={textContent} />
              </h4>
            );
          },
          h5: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <h5 {...props}>
                <InlineGlossaryText text={textContent} />
              </h5>
            );
          },
          h6: ({ node, children, ...props }) => {
            const textContent = extractTextFromChildren(children);
            return (
              <h6 {...props}>
                <InlineGlossaryText text={textContent} />
              </h6>
            );
          },
          // Handle code blocks without tooltip processing to avoid issues
          code: ({ node, children, ...props }) => (
            <code {...props}>{children}</code>
          ),
          pre: ({ node, children, ...props }) => (
            <pre {...props}>{children}</pre>
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}
